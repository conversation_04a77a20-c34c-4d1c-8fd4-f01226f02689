// console.log(process.env.BLM_API_ADMIN)
export const proxy = {
  '/api': {
    target: 'http://jsonplaceholder.typicode.com/',
    changeOrigin: true,
    pathRewrite: { '^/api': '' },
  },
  '/admin': {
    target: process.env.BLM_API_ADMIN,
    changeOrigin: true,
    pathRewrite: { '^/admin': '/admin' },
  },
  '/marketingCms': {
    target: process.env.BLM_API_ADMIN,
    changeOrigin: true,
  },
  '/bos/admin': {
    target: process.env.BLM_API_ADMIN,
    changeOrigin: true,
    pathRewrite: { '^/bos/admin': '/bos/admin' },
  },
  '/export/*': {
    target: process.env.BLM_API_EXPORT,
    changeOrigin: true,
  },
  '/oss': {
    target: process.env.BLM_API_OSS,
    changeOrigin: true,
    pathRewrite: { '^/oss': '/oss' },
  },
  '/iconfont/*': {
    target: process.env.BLM_API_FONT,
    changeOrigin: true,
  },
  '/lego': {
    target: 'https://hubble-test.yueyuechuxing.cn',
    changeOrigin: true,
    pathRewrite: { '^/lego': '/lego' },
  },
  '/saasApi': {
    target: 'https://hubble-test.yueyuechuxing.cn',
    changeOrigin: true,
    pathRewrite: { '^/saasApi': '/saasApi' },
  },
  '/customerApi': {
    target: 'https://hubble-test.yueyuechuxing.cn',
    changeOrigin: true,
    pathRewrite: { '^/customerApi': '/customerApi' },
  },
  '/dockingApi': {
    target: 'https://hubble-test.yueyuechuxing.cn',
    changeOrigin: true,
    pathRewrite: { '^/dockingApi': '/dockingApi' },
  },
};

const buildExternals = {
  '@blm-fe/bos-proxy': 'BlmBosProxy',
  'blm-utils': 'BlmUtils',
};

const devExternals = {
  // '@blmcp/ui': 'blmcpUi',
  // '@blmcp/peento-businessComponents': 'blmBusinessComponents',
  // '@blmcp/ui-mobile': 'mobileLib',
};

export const externals = {
  dayjs: 'dayjs',
  antd: 'antd',
  react: 'var window.React',
  'react-dom': 'var window.ReactDOM',
  'prop-types': 'var window.PropTypes',
  '@alilc/lowcode-editor-skeleton':
    'var window.AliLowCodeEngine.common.skeletonCabin',
  '@alilc/lowcode-engine-ext': 'var window.AliLowCodeEngineExt',
  '@alilc/lowcode-engine': 'var window.AliLowCodeEngine',
  moment: 'var window.moment',
  lodash: 'var window._',
  ...(process.env.NODE_ENV === 'development' ? devExternals : buildExternals),
};
