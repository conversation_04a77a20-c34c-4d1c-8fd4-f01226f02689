const exec = require('child_process').exec;
const util = require('util');

const execPromise = util.promisify(exec);
const execSync = require('child_process').execSync;

const BRANCH_NAME = execSync('git rev-parse --abbrev-ref HEAD')
  .toString()
  .replace(/\s+/, '');
const VERSION = BRANCH_NAME.split('/')[1];
const PROJECTNAME = 'qbi'
const OUTPATH = `https://${process.env.UMI_ENV === 'production' ? 'webstatic' : 'cdntest'}.yueyuechuxing.cn/yueyue/admin/${PROJECTNAME}/${VERSION}${process.env.BUILD_ENV ? `/${process.env.BUILD_ENV}` : ''}/lowcode/`

module.exports = ({ onHook, onGetWebpackConfig, getAllTask }, options = {}) => {
  const script = 'bigbang --config=.lowcode.rule';
  /** dev 模式扫描 */
  let open = true;
  onHook('after.start.compile', async (stats) => {
    if (open) {
      await execPromise(script);
      open = false;
    } else {
      open = true;
    }
  });
  /** buikd 模式扫描 */
  onHook('before.build.run', async (stats) => {
    await execPromise(script);
  });

  /** 注入打包忽略 */
  onGetWebpackConfig((config) => {
    config.output.publicPath(process.env.NODE_ENV === 'development' ? 'http://localhost:3333/' : OUTPATH)
    config.module.rules.delete('svg');
    config.module
      .rule('svg')
      .test(/\.svg$/)
      .use('svgr')
      .loader('@svgr/webpack');

    const externals = {
      antd: 'antd',
      echarts: 'var window.echarts',
      dayjs: 'var window.dayjs',
      moment: 'moment',
      '@blmcp/peento-request': 'legoRequest',
      'lodash-es': '_',
      'lodash': '_',
      // '@blm-fe/bos-proxy': 'var window.BlmBosProxy',
      // 'blm-utils': 'var window.BlmUtils',
      '@blmcp/ui': 'var window.blmcpUi',
      '@blmcp/peento-businessComponents': 'var window.blmBusinessComponents',
      '@blmcp/ui-mobile': 'var window.mobileLib',
    }

    if(process.env.NODE_ENV !== 'development'){
      externals['@blm-fe/bos-proxy'] = 'var window.BlmBosProxy';
      externals['blm-utils'] = 'var window.BlmUtils';
    }

    config.merge({
      externals,
    });

    // config.module
    //   .rule('images')
    //   .test(/\.png$/)
    //   .use('url-loader')
    //   .loader('url-loader')
    //   .tap(options => {
    //     if(!options){
    //       return {
    //         limit: 50000
    //       }
    //     }
    //     options.limit = 50000; // 小于50kb的图片会被转为base64
    //     return options;
    //   });
  });
};
