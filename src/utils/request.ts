/*
 * @Description: 请输入....
 * @Author: g<PERSON><PERSON><PERSON><PERSON>@bailongma-inc.com
 * @Date: 2025-05-26 20:53:59
 * @LastEditTime: 2025-06-13 18:19:06
 * @LastEditors: gao<PERSON><PERSON><PERSON>@bailongma-inc.com
 */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import { createRequest } from '@blmcp/peento-request';
import { message } from '@blmcp/ui';
// import { bosProxyRequests, bosProxyResponses } from 'blm-utils';
import * as BlmUtils from 'blm-utils';
import { isHubble, HubbleApi } from '@/utils/hubble';
import { tmpbosProxyMap } from '@/utils/bosProxy/bosProxyMapV2';

const request = createRequest({
  onRefreshToken: () => {
    window.$ReactRefreshToken && window.$ReactRefreshToken();
  },
  onLogout: () => {
    window.$ReactLogOut && window.$ReactLogOut();
  },
  responseNoReject: true,
});

request.interceptors.request.use(
  (config: any) => {
    // 哈勃环境下 替换url
    if (isHubble) {
      config.url =
        HubbleApi[config.url] ||
        config.url.replace(/^\/admin\/v1\//, '/lego/admin/v1/');
    }
    return (
      BlmUtils?.bosProxyRequests?.(config, request, 'bi', tmpbosProxyMap) ||
      config
    );
    // return config;
  },
  (error) => Promise.reject(error),
);

request.interceptors.response.use(
  (response) => {
    if (response.config) {
      if (typeof response.data === 'object' && response.data.code !== 1) {
        if (!response.config.notMessage) {
          const msg = response.data.tips || response.data.msg;
          message.error(msg);
        }
        return Promise.reject(response.data);
      }
    }
    return BlmUtils?.bosProxyResponses?.(response, tmpbosProxyMap) || response;
  },
  (error) => {
    const msg = typeof error === 'object' ? error.msg : error;
    // 增加全局错误提示， 入参中包含error_code的场景，返回的错误信息为对象，在业务中单独处理
    if (typeof msg === 'string') {
      if (msg === '请勿重复请求') return Promise.reject(msg);
      if (msg.includes('请求超时')) {
        message.error('接口请求超时');
      } else {
        message.error(error);
      }
    }
    return Promise.reject(error);
  },
);

export default request;
