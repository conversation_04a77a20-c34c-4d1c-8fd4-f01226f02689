/*
 * @Description: 请输入....
 * @Author: g<PERSON><PERSON><PERSON><PERSON>@bailongma-inc.com
 * @Date: 2025-06-09 18:03:29
 * @LastEditTime: 2025-06-12 18:21:02
 * @LastEditors: gao<PERSON><PERSON><PERSON>@bailongma-inc.com
 */
import { isHubble } from './hubble';

if (process.env.NODE_ENV === 'development') {
  window.$oriAndCategoryFlag = true;
  window.$BLMReleaseCenter = {};
  window.$BLMReleaseCenter.getSceneSwitch = function () {
    return window.$oriAndCategoryFlag;
  };
}

export const legoGeneralizeSwitch = (sceneKey = 'legoGeneralize') => {
  if (process.env.NODE_ENV === 'development') {
    return true;
  }
  const devSwitch = window?.$HbUtils?.releaseCenter?.getSceneSwitch?.(
    `${sceneKey}-hb`,
  );
  // @ts-ignore
  const useSwitch = window?.$BLMReleaseCenter?.getSceneSwitch(sceneKey);
  const sceneSwitch = isHubble ? devSwitch : useSwitch;
  return sceneSwitch ?? false;
};
