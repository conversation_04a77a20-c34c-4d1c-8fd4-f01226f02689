import LegoEdit from '@blm/bi-lego-sdk/LegoEditView';
import { event } from '@blm/bi-lego-sdk/utils';
import React from 'react';
import {
  blmAnalysisPageView,
  blmAnalysisModuleClick,
  blmMicrofs,
} from '@/utils/eventTracking';

export default function () {
  React.useEffect(() => {
    event.on('init', () => {
      blmAnalysisPageView({
        pageId: 'p_leopard_cp_00000400',
        eventId: 'e_leopard_cp_pv_00001782',
      });
    });
    event.on('loaded', () => {
      blmMicrofs();
    });
    event.on('templateExport', ({ reportId }) => {
      blmAnalysisModuleClick({
        pageId: 'p_leopard_cp_00000400',
        eventId: 'e_leopard_cp_click_00003238',
        ext: {
          str0_e: reportId,
        },
      });
    });
  }, []);
  return (
    <LegoEdit
      getExtraEnvironment={() => {
        const qiankunDom = document.getElementsByTagName('qiankun-head')?.[0];
        if (qiankunDom) {
          const htmlStr = qiankunDom.innerHTML;
          const scripts =
            htmlStr.match(/script\s([^]*?)replaced by import-html-entry/g) ||
            [];
          const links = htmlStr.match(/\/\*\shttp[^]*?.css\s\*\//g) || [];
          const filterScript = scripts
            .filter((f) =>
              [
                'antd-umd',
                'antdIcons-umd',
                'blmcpUi-umd',
                'blmBusinessComponents-umd',
              ].some((k) => f.includes(k)),
            )
            .map((v) =>
              v.replace(/script|replaced\s+by\s+import-html-entry|\s/g, ''),
            )
            .filter((f) => /^https:\/\/[^]*?.js$/.test(f));
          const filterLink = links
            .filter((f) =>
              ['antd-umd', 'blmcpUi-umd', 'blmBusinessComponents-umd'].some(
                (k) => f.includes(k),
              ),
            )
            .map((v) => v.replace(/\/\*|\s|\*\//g, ''))
            .filter((f) => /^https:\/\/[^]*?.css$/.test(f));

          if (filterScript.length === 4 && filterLink.length === 3) {
            return [...filterLink, ...filterScript];
          } else {
            console.log('extraEnvironment 获取错误, 配置文件丢失');
          }
        } else {
          console.log('extraEnvironment 获取错误，不存在qiankunDom');
        }
      }}
    />
  );
}
