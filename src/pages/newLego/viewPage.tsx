import LegoRender from '@blm/bi-lego-sdk/LegoRenderView';
import { event } from '@blm/bi-lego-sdk/utils';
import React from 'react';
import {
  blmAnalysisPageView,
  blmAnalysisModuleClick,
  blmMicrofs,
} from '@/utils/eventTracking';

export default function () {
  React.useEffect(() => {
    event.on('init', ({ reportId }) => {
      blmAnalysisPageView({
        pageId: 'p_leopard_cp_00000662',
        eventId: 'e_leopard_cp_pv_00003064',
        ext: {
          str0_e: reportId,
        },
      });
      blmAnalysisPageView({
        pageId: 'p_leopard_cp_00001012',
        eventId: 'e_leopard_cp_pv_00004304',
        ext: {
          str0_e: reportId,
        },
      });
    });
    event.on('loaded', () => {
      blmMicrofs();
    });
    event.on('templateExport', ({ reportId }) => {
      blmAnalysisModuleClick({
        pageId: 'p_leopard_cp_00000400',
        eventId: 'e_leopard_cp_click_00003238',
        ext: {
          str0_e: reportId,
        },
      });
    });

    event.on('templateEdit', ({ reportId }) => {
      blmAnalysisModuleClick({
        eventId: 'e_leopard_cp_click_00001098',
        pageId: 'p_leopard_cp_00000344',
        ext: {
          int0_e: reportId,
        },
      });
    });
    event.on('templateShare', ({ reportId, reportType }) => {
      blmAnalysisModuleClick({
        eventId: 'e_leopard_cp_click_00003806',
        pageId: 'p_leopard_cp_00000884',
        ext: {
          str0_e: reportType,
          str1_e: reportId,
        },
      });
    });
  }, []);
  return <LegoRender />;
}
