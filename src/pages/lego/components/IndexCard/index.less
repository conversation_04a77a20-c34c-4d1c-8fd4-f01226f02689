.lego-index-card-com-wrap {
  * {
    font-family: PingFang SC;
  }
  &.mobile {
    // 移动版样式做特殊处理
    .lego-index-card-measure-value {
      font-size: 28px;
      text-align: left;
    }
  }

  .lego-card-com-right-info{
    color: red;
    float: right;
    cursor: pointer;
    color: #ED7B2F;
  }

  .ant-card-bordered {
    border: none;

    .ant-card-body {
      padding: 0 !important;
      height: 100%;
    }
  }

  .lego-index-card-title {
    font-size: 13px;
    color: #000;
    font-weight: 500;
    line-height: 20px;
  }

  .lego-index-card-measure-value {
    text-align: center;
    font-size: 34px;
    font-weight: 700;
    color: rgba(0, 0, 0, 0.9);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .lego-index-card-measure-trend {
    margin-top: 10px;
    height: 40px;
    float: right;
    width: 45%;
  }

  .lego-index-card-measure-value-compare {
    padding-left: 6px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    line-height: 18px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .lego-index-card-measure-value-explain {
    margin: 6px 0 0 0;
    padding: 10px;
    border-radius: 6px;
    background-color: #f5f6f7;
    font-size: 12px;
    min-height: calc(100% - 95px);

    &.tooltip:hover {
      background: #eeeef0;
    }

    .lego-index-card-measure-value-explain-text {
      display: -webkit-box;
      overflow: hidden;
      line-height: 18px;
      // -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
      white-space: normal !important;
      word-wrap: break-word;
    }
  }
}
