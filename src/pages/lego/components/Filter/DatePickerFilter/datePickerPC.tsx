import React, { useState } from 'react';
import { DatePicker, ConfigProvider } from '@blmcp/ui';
import locale from 'antd/es/date-picker/locale/zh_CN';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import 'dayjs/locale/zh-cn';
import type { DateRangePickerProps, DisabledDate } from './type';
import { componentClassPrefix } from './config';
import { ArrRightO, ArrLeftO, ArrRightDO, ArrLeftDO, DateO } from './icon';

const { RangePicker }: any = DatePicker;

// const defaultProps: DateRangePickerProps = {};

export const DatePickerPC = (props: DateRangePickerProps) => {
  const [dates, setDates] = useState<any>(null);
  const [value, setValue] = useState<[Dayjs, Dayjs] | undefined>(
    props.defaultValue?.length === 2
      ? [dayjs(props.defaultValue[0]), dayjs(props.defaultValue[1])]
      : undefined,
  );
  const { presets } = props;
  // 如果设置了presets，需要转换格式Date->Dayjs
  let pcPreset;
  if (presets?.length) {
    pcPreset = presets.map((item) => {
      const { value } = item;
      return {
        label: item.label,
        value: [dayjs(value[0]), dayjs(value[1])],
      };
    });
  }
  // 如果设置了defaultValue，需要转换格式Date->Dayjs
  let pcDefaultValue;
  if (props?.defaultValue?.length === 2) {
    pcDefaultValue = [
      dayjs(props.defaultValue[0]),
      dayjs(props.defaultValue[1]),
    ];
  }

  const disabledDate: DisabledDate = (current: Dayjs, info?: any) => {
    if (props.max) {
      if (current > dayjs(props.max).endOf('day')) {
        return true;
      }
    }
    if (props.min) {
      if (current < dayjs(props.min).startOf('day')) {
        return true;
      }
    }
    // antd从5.14.0开始提供了info参数，但该变化具有破坏性：
    // 旧版如果选择起始时间，则面板内早于该时间的cell在disabledDate函数中遍历到，但内置早于此的时间是不可选
    // 新版如果选择起始时间，则面板内早于该时间的cell在disabledDate函数中不会遍历到，但内置早于此的时间是可选
    // 上述问题导致旧版本做跨度限制的方式在新版本中不兼容
    // 如果限制了时间跨度
    if (props.maxStep) {
      // 有info是新版（5.14.0），则用新版的方式做跨度限制
      if (info?.from) {
        return Math.abs(current.diff(info.from, 'days')) > props.maxStep;
      }
      if (dates?.[0] && current.diff(dates[0], 'days') > props.maxStep) {
        return true;
      }
      if (dates?.[1] && dates[1].diff(current, 'days') > props.maxStep) {
        return true;
      }
    }
    return false;
  };

  // 弹出日历和关闭日历的回调
  const onOpenChange = (open: boolean) => {
    if (open) {
      setDates([null, null]);
    } else {
      setDates(null);
    }
  };
  // 待选日期发生变化的回调
  const onCalendarChange = (val: any) => {
    setDates(val);
  };
  const onChange = (value: any) => {
    setValue(value);
    props?.onChange?.([
      value[0]?.startOf('day')?.toDate(),
      value[1]?.endOf('day')?.toDate(),
    ]);
  };
  return (
    <ConfigProvider
      theme={{
        token: {},
      }}
    >
      <RangePicker
        value={dates || value}
        onChange={onChange}
        onOpenChange={onOpenChange}
        onCalendarChange={onCalendarChange}
        disabledDate={disabledDate}
        locale={locale}
        allowClear={false}
        defaultValue={pcDefaultValue}
        presets={pcPreset}
        nextIcon={<ArrRightO />}
        prevIcon={<ArrLeftO />}
        superNextIcon={<ArrRightDO />}
        superPrevIcon={<ArrLeftDO />}
        suffixIcon={<DateO />}
        getPopupContainer={props.getPopupContainer}
        className={`${componentClassPrefix} ${componentClassPrefix}-pc`}
        {...(props.config || {})}
      />
    </ConfigProvider>
  );
};
