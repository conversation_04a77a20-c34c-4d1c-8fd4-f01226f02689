import React, { useRef, useState } from 'react';
import { Tooltip, message, Input } from '@blmcp/ui';
// import { ExclamationCircleOutlined } from '@ant-design/icons';
import { reportStore } from '@blm/bi-lego-sdk/dist/es/utils';
import useComponent from '@/pages/lego/hooks/useComponent';
import { isHubble } from '@/utils/hubble';

import {
  setSelectComponentPropsDataById,
  walkerComponents,
  getProjectSchema,
} from '@/pages/lego/utils';
import { globalCache } from '@/pages/lego/utils/cache';
import { blmAnalysisModuleClick } from '@/utils/eventTracking';
import isMobile from '../../../utils/isMobile';
import { LegoExport } from '../../LegoExport';
import DrageIcon from './icon/ic-dragBar-o.svg';
// import NoteIcon from './icon/note.svg';
import Arrow from './icon/arrow.svg';

interface FilterType {
  bizColumn: string;
  value: string[];
  displayValue: string;
  overflowTooltip: boolean;
}

// import CopyIcon from './icon/copy.svg';
// import DeleteIcon from './icon/delete.svg';
import './style.less';

export default (props: any) => {
  // const [edit, setEdit] = useState(false);
  // const editRef = useRef<HTMLDivElement>();
  const [meta, setMeta] = useComponent(props.componentId);

  // 搜索字段获取
  const [searchParams, setSearchParams] = useState({});
  const [otherParams, setOtherParams] = useState({});

  const title = props.title || meta.title;
  const clickTime = useRef(null);
  const inputRef = useRef(null);
  // 是否编辑态
  const [isEdit, setEditState] = useState(false);
  // const [newtitle] = useState(title);

  // const copyClick = function () {
  //   const node = getNodeById(props.componentId);
  //   node?.parent?.insertAfter({
  //     componentName: node.componentName,
  //     props: {
  //       ...node.propsData,
  //     },
  //   } as any);
  // };
  // const deleteClick = function () {
  //   Modal.confirm({
  //     title: '您确认要删除该组件吗？',
  //     okText: '确定',
  //     cancelText: '取消',
  //     onOk: () => {
  //       getNodeById(props.componentId)?.remove();
  //     },
  //   });
  // };

  const handleExportBeforeOpen = () => {
    try {
      blmAnalysisModuleClick({
        pageId: 'p_leopard_cp_00000662',
        eventId: 'e_leopard_cp_click_00003236',
        ext: {
          str0_e: props.reportId,
        },
      });
      const oldValue = globalCache?.relationshipMap?.[props.componentId]?.[2];

      const query = JSON.parse(oldValue);
      const filterInfo = query?.filterInfo ?? [];

      const filters: FilterType[] = [];
      filterInfo.forEach((filter) => {
        filters.push({
          bizColumn: filter.key,
          value: filter.fieldValue,
          displayValue: filter.fieldLabel?.join('、'),
          overflowTooltip: true,
        });
      });

      setSearchParams(filters);
      setOtherParams(query);
    } catch (e) {
      message.error(e);
    }

    return true;
  };
  const titleRef = useRef(null);
  // useEffect(() => {
  //   if (titleRef?.current) {
  //     titleRef.current.style.maxWidth = props.width + 24 + 'px';
  //   }
  // }, [props.width]);

  return (
    <div
      ref={titleRef}
      className={`lego-bi-compontent-title-box ${isMobile() ? 'mobile' : ''}`}
      style={{ borderWidth: props.titleConfig.border ? '1px' : '0px' }}
    >
      {props.__designMode === 'design' && (
        <DrageIcon style={{ marginRight: '7px', verticalAlign: 'sub' }} />
      )}
      <div className="lego-bi-compontent-title">
        {isEdit ? (
          <Input
            size="small"
            ref={inputRef}
            data-edit
            defaultValue={title}
            onBlur={(event: React.FocusEvent<HTMLInputElement>) => {
              const value = event.target.value;
              walkerComponents(getProjectSchema(), (item, meta) => {
                let info;
                if (
                  (!value.trim() && (info = '标题名称不能为空')) ||
                  (item.id !== props.componentId &&
                    (item.props?.title || meta.title) === value &&
                    (info = '标题名称不能重复')) ||
                  (value.length > 48 && (info = '最长不超过48个字'))
                ) {
                  if (info === '标题名称不能为空') {
                    inputRef.current.input.value = title;
                  }
                  message.error(info);
                  inputRef.current?.focus?.({
                    cursor: 'all',
                  });
                  return false;
                }
              }).then(() => {
                setMeta({ title: value }, true);
                setEditState(false);
                setSelectComponentPropsDataById(
                  props.componentId,
                  'title',
                  value,
                );
              });
            }}
          />
        ) : (
          <Tooltip title={title} placement="topLeft">
            <h1
              onClick={() => {
                // 仅设计模式生效
                if (props.__designMode !== 'design') return false;
                if (clickTime.current && Date.now() - clickTime.current < 300) {
                  clickTime.curren = null;
                  // 认为双击执行
                  setEditState(true);
                  setTimeout(() => {
                    inputRef.current!.focus({
                      cursor: 'all',
                    });
                  });
                } else {
                  clickTime.current = Date.now();
                }
              }}
              style={{ textOverflow: 'ellipsis' }}
            >
              {title}
            </h1>
          </Tooltip>
        )}
        {/* {props.describe && (
          <Tooltip title={props.describe} placement="top">
            <NoteIcon />
          </Tooltip>
        )} */}
      </div>
      {/* 导出按钮 */}
      {!props.isEdit &&
      meta.componentType !== 5 &&
      !isMobile() &&
      !isHubble &&
      (reportStore.get(props.uuid)
        ? reportStore.get(props.uuid).canExport
        : true) &&
      props.isRender ? (
        <div className="lego-export">
          {/* <ExportIcon /> */}
          <LegoExport
            searchParams={searchParams}
            otherParams={otherParams}
            handleExportBeforeOpen={handleExportBeforeOpen}
            uuid={props.uuid}
          />
        </div>
      ) : null}
      {/* 无车队提示 */}
      {/* {props.fieldTips && (
        <Tooltip
          arrow={{ pointAtCenter: true }}
          placement="topRight"
          title="部分图表的数据集中缺少字段，筛选条件未生效"
        >
          <ExclamationCircleOutlined style={{ color: '#ED7B2F' }} />
        </Tooltip>
      )} */}
      {/* 右箭头按钮 文本移动端用 */}
      {props.showTitleArrow && (
        <span className="arrow-svg">
          <Arrow></Arrow>
        </span>
      )}
      {/* {props.__designMode === 'design' && (
        <div className="lego-bi-compontent-title-fun">
          <Tooltip title="复制" placement="top">
            <CopyIcon onClick={copyClick} />
          </Tooltip>
          <Tooltip title="删除" placement="top">
            <DeleteIcon onClick={deleteClick} />
          </Tooltip>
        </div>
      )} */}
    </div>
  );
};
