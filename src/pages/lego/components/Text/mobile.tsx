// 移动版
import React, { useState } from 'react';
import { Popup, Ellipsis } from '@blmcp/ui-mobile';
import useComponent from '@/pages/lego/hooks/useComponent';
import '@blmcp/ui-mobile/dist/es/style/theme-default.css';

export default (props: any) => {
  const [meta] = useComponent(props.componentId);
  const [visible, setVisible] = useState(false);
  const title = props.title || meta.title;
  return (
    <div
      className="lego-text-wrap lego-bi-scroll-hide mobile"
      onClick={() => setVisible(true)}
      style={{ minHeight: '20px' }}
    >
      <Ellipsis content={props.text} rows={3} direction="end"></Ellipsis>
      <Popup
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
        }}
        className="lego-text-popup-wrapper"
        onMaskClick={() => {
          setVisible(false);
        }}
        visible={visible}
      >
        <div className="lego-text-popup-header">{title}</div>
        <div className="lego-text-popup-content">{props.text}</div>
      </Popup>
    </div>
  );
};
