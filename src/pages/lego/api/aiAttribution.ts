import request from '@/utils/request';

// **************************** 智能归因 ****************************

export const queryRuleConfigByIdOrAdcode = (params) => {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/queryRuleConfigByAdcode',
    method: 'POST',
    data: params,
  });
};
// 二阶段 获取分层维度列表 新接口
export const bosQueryRuleConfigByAdcode = (params = {}) => {
  return request({
    url: '/bos/admin/v1/ai/marketing/attr/queryRuleConfigByAdcode',
    method: 'POST',
    data: params,
  });
};
// 智能归因一期 - 首屏+报告接口（单城市权限）
export const queryBaseCntOrderDoneChartReport = (params) => {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/queryBaseCntOrderDoneChartReport',
    method: 'POST',
    data: params,
  });
};
// 智能归因二期 - 首屏+报告接口（多城市权限）
export const queryMultiCityChartReport = (params) => {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/queryMultiCityChartReport',
    method: 'POST',
    data: params,
  });
};
// 智能归因-分层条件配置-查询
export const queryRuleConfigList = (params) => {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/queryRuleConfigList',
    method: 'POST',
    data: params,
  });
};
// 智能归因-分层条件配置-新增
export const addRuleConfig = (params) => {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/addRuleConfig',
    method: 'POST',
    data: params,
  });
};
// 智能归因-分层条件配置-编辑
export const updateRuleConfig = (params) => {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/updateRuleConfig',
    method: 'POST',
    data: params,
  });
};
// 智能归因-分层条件配置-删除
export const deleteRuleConfig = (params) => {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/deleteRuleConfig',
    method: 'POST',
    data: params,
  });
};

// 智能归因-分层条件配置-新增校验
export const addRuleConfigExistCheck = (params) => {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/addRuleConfigExistCheck',
    method: 'POST',
    data: params,
  });
};
// 智能归因-分层条件配置-新增校验
export const bosAddRuleConfigExistCheck = (params = {}) => {
  return request({
    url: '/bos/admin/v1/ai/marketing/attr/addRuleConfigExistCheck',
    method: 'POST',
    data: params,
  });
};
// 归因详情基础数据
export function queryBaseCntOrderDoneReport(params = {}) {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/queryBaseCntOrderDoneReport',
    method: 'POST',
    data: params,
  });
}

// 归因详情-时段分层/司机分层
export function queryLayerChartsByPath(params = {}) {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/queryLayerChartsByPath',
    method: 'POST',
    data: params,
  });
}

// 归因详情-全分层
export function queryAllLayerChartsByPath(params = {}) {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/queryAllLayerChartsByPath',
    method: 'POST',
    data: params,
  });
}

// 城市明细数据列表
export function queryCityDetailList(params = {}) {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/queryCityDetailList',
    method: 'POST',
    data: params,
  });
}
// 城市明细报告
export function queryCityDetailReport(params = {}) {
  return request({
    url: '/admin/v2/ai/inspire/marketing/attr/queryCityDetailReport',
    method: 'POST',
    data: params,
  });
}
