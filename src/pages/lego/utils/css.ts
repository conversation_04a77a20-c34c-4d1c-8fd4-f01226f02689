/*
 * Created on Tue Jan 30 2024
 *
 * Copyright (c) 2024 bai long ma
 */

// 给指定的dom元素设置css样式
const css = (el: HTMLElement, styles: { [key: string]: string }) => {
  Object.keys(styles).forEach((key: string) => {
    el.style.setProperty(key, styles[key]);
  });
};

// 创建一个dom元素
const element = (
  tag: string,
  property: { [key: string]: any },
  parentNode: HTMLElement,
) => {
  const dom = document.createElement(tag);
  Object.keys(property).forEach((key: string) => {
    if (key === 'css') {
      css(dom, property[key]);
    } else if (key === 'innerHTML') {
      dom.innerHTML = property.innerHTML;
    }
  });
  parentNode.appendChild(dom);
  return {
    dom,
    remove: () => {
      parentNode.removeChild(dom);
    },
  };
};

// 计算一个dom元素填充文案之后的高度和宽度，可用于测量省略号
export const getTextyWidthAndHeight = (
  text: string,
  styles: object,
  tag = 'div',
) => {
  const el = element(tag, { css: styles, innerHTML: text }, document.body);
  // getBoundingClientRect的精度更高
  let width, height;
  if (el?.dom?.getBoundingClientRect) {
    width = el.dom.getBoundingClientRect().width;
    height = el.dom.getBoundingClientRect().height;
  } else {
    width = el.dom.offsetWidth;
    height = el.dom.offsetHeight;
  }
  el.remove();
  return { width, height };
};

// 计算当前table表格表头的最大宽度
// 动态计算列表头宽度，内容宽度根据table宽度自适应
export const calculateColumnWidth = (title: string, maxWidth = 100) => {
  // 分组表头可能需要更多的空间来容纳子列
  const baseWidth = title?.length * 14 + 32 + 12 + 10 || 0;
  return Math.max(baseWidth, maxWidth);
};

// // 计算分组表头的总宽度（基于其子列的宽度）
// export const calculateGroupColumnWidth = (children: any[]): number => {
//   if (!children || children.length === 0) return 120;

//   // 计算所有子列的宽度总和
//   const totalChildWidth = children.reduce((sum, child) => {
//     if (child.children) {
//       return sum + calculateGroupColumnWidth(child.children);
//     }
//     return sum + (child.width || 100);
//   }, 0);

//   return Math.max(totalChildWidth, 120);
// };

// // 后处理函数：为分组表头设置正确的宽度
// export const postProcessColumnWidths = (columns: any[]): any[] => {
//   return columns.map((column) => {
//     if (column.children && column.children.length > 0) {
//       // 递归处理子列
//       const processedChildren = postProcessColumnWidths(column.children);
//       // 计算分组表头的宽度为所有子列宽度之和
//       const totalChildWidth = calculateGroupColumnWidth(processedChildren);
//       const headerTextWidth = calculateColumnWidth(column.title, true);

//       return {
//         ...column,
//         children: processedChildren,
//         width: Math.max(totalChildWidth, headerTextWidth), // 取子列总宽度和表头文本宽度的最大值
//       };
//     }
//     return column;
//   });
// };
