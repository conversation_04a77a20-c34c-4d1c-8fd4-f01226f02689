import { createContext } from '@/utils/store';
import { setVariable } from '@/utils/common';

const variable = setVariable('__lego__bi_filterContext__', {});

interface State {
  city: string[];
  carTeam: string[];
  fleet: string[];
  tenant: number | null;
}

function reducer(state: State, action: { type: string; payload: any }) {
  switch (action.type) {
    case 'tenant':
      return {
        ...state,
        tenant: action.payload,
        city: [],
        carTeam: [],
        fleet: [],
      };
    case 'city':
      return { ...state, city: action.payload, carTeam: [], fleet: [] };
    case 'carTeam':
      return { ...state, carTeam: action.payload, fleet: [] };
    case 'fleet':
      return { ...state, fleet: action.payload };
    case 'clear':
      return { ...state, fleet: [], carTeam: [], city: [] };
    default:
      return state;
  }
}

export default function createFilterContext(reportId: string) {
  if (!reportId) {
    console.error('reportId is required');
  }
  return (
    variable[reportId] ||
    (variable[reportId] = createContext(reducer, {
      city: [],
      carTeam: [],
      fleet: [],
      tenant: null,
    }))
  );
}
