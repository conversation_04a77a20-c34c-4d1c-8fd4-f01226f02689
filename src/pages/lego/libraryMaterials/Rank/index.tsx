import { Rank as RankView } from '@/pages/lego/components';
import type { ComponentProps } from '../../type';
import ComponentWrapper from '../../components/module/ComponentWrapper';
import { handleData } from './handleData';
import isMobile from '../../utils/isMobile';

export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: '维度',
          key: 'dimensionInfo',
          placeholder: '仅支持拖入 1 个字段',
          onlyOne: true,
          limit: [1, 1],
          numberFormatConfig: false,
          // 只格式化指标类字段
          numberFormatIndexOnly: true,
          disabledSortConfig: false,
          // 不加默认聚合方式
          ignoreSetDefaultComputeType: true,
        },
        {
          label: '数值',
          key: 'measureInfo',
          rateDependKey: 'dimensionInfo',
          dateFormatDisabled: true,
          numberFormatConfig: true,
          // 维度聚合方式
          combineModeDim: {
            aggregate: [
              {
                key: 1,
                label: '计数',
              },
              {
                key: 2,
                label: '去重计数',
              },
            ],
          },
          combineModeIndex: {
            aggregate: [
              {
                key: 6,
                label: '求和',
              },
              {
                key: 1,
                label: '计数',
              },
              {
                key: 5,
                label: '平均值',
              },
              {
                key: 2,
                label: '去重计数',
              },
              {
                key: 3,
                label: '最大值',
              },
              {
                key: 4,
                label: '最小值',
              },
            ],
            quick: [
              {
                key: 'rate',
                label: '同环比',
              },
              {
                key: 'percent',
                label: '百分比',
                column: true,
              },
            ],
          },
          onlyOne: true,
          placeholder: '仅支持拖入 1 个字段',
          // 指标聚合方式
          limit: [1, 1],
        },
        {
          label: '辅助指标',
          key: 'secondary',
          type: 'merge',
          to: 'measureInfo',
          rateDependKey: 'dimensionInfo',
          dateFormatDisabled: true,
          numberFormatConfig: true,
          combineModeDim: {
            aggregate: [
              {
                key: 1,
                label: '计数',
              },
              {
                key: 2,
                label: '去重计数',
              },
            ],
          },
          combineModeIndex: {
            aggregate: [
              {
                key: 6,
                label: '求和',
              },
              {
                key: 1,
                label: '计数',
              },
              {
                key: 5,
                label: '平均值',
              },
              {
                key: 2,
                label: '去重计数',
              },
              {
                key: 3,
                label: '最大值',
              },
              {
                key: 4,
                label: '最小值',
              },
            ],
            quick: [
              {
                key: 'rate',
                label: '同环比',
              },
              {
                key: 'percent',
                label: '百分比',
                column: true,
              },
            ],
          },
          limit: [0, Infinity],
        },
      ],
    },
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 3,
  // 该组件用到的数据类型
  dataType: 42,
};

export const Rank = (props: ComponentProps<any>) => {
  const dimensionInfo = props?.dataSetConfig?.dimensionInfo ?? [];
  const measureInfo = props?.dataSetConfig?.measureInfo ?? [];
  const secondary = props?.dataSetConfig?.secondary ?? [];
  return ComponentWrapper(RankView, {
    // 增加数据处理层
    handleData: handleData([
      ...dimensionInfo.map((item, index) => ({
        ...item,
        dataIndex: item.key + '_D' + index,
        type: 'dimension',
        typeIndex: index,
      })),
      ...[...measureInfo, ...secondary].map((item, index) => ({
        ...item,
        dataIndex: item.key + '_M' + index,
        type: 'measure',
        typeIndex: index,
      })),
    ]),
    // 标题配置
    titleConfig: {
      show: true,
      border: false,
    },
    notDefaultStyle: true,
    defaultHeight: isMobile() ? 'auto' : undefined,
    wrapperStyle: {
      width: '100%',
      height: isMobile() ? undefined : 'calc(100% - 24px)',
      overflow: 'auto',
      marginTop: isMobile() ? 0 : '-12px',
    },
    wrapperClass: 'lego-table-container',
  })(props);
};
Rank.displayName = '排行榜';
