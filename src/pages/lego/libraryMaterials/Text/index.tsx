import ComponentWrapper from '../../components/module/ComponentWrapper';
import { TextComponent } from '../../components/Text';
import { ComponentProps } from '@/pages/lego/type';
import isMobile from '../../utils/isMobile';

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 5,
};

export const LinkSetterComponent = {
  dataSetConfig: {
    componentName: 'DescriptionConfig',
    props: {
      desc: '请在左侧布局区域输入文字内容',
    },
  },
};

export const Text = function (props: ComponentProps<any>) {
  return ComponentWrapper(TextComponent, {
    notQueryData: true,
    notDefaultStyle: false,
    defaultHeight: isMobile() ? 'auto' : 126,
    showTitleArrow: isMobile(),
  })(props);
};

Text.displayName = '文本';
