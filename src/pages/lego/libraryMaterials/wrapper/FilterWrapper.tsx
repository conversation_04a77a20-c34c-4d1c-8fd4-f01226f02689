import React, { useState, useEffect, useMemo, useLayoutEffect } from 'react';
import queryCenterExp from '@/pages/lego/libraryMaterials/module/Query';
import { DatasetItem } from '@/pages/lego/type';
import linkageCenterExp from '@/pages/lego/libraryMaterials/module/Linkage';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import useLayoutUpdateEffect from '@/hooks/useLayoutUpdateEffect';
import './index.less';
// import { globalCache } from '@/pages/lego/utils/cache';

interface Props {
  defaultValue?: any;
  loading_data: boolean;
  loading: boolean;
  filterKey?: string;
  handleInject?: (setting: { fieldItem: DatasetItem; value: any }) => void;
  children: React.ReactNode | ((props: any) => React.ReactNode);
  componentProps: any;
  fieldProps?: DatasetItem;
  handleFieldLabel?: (value: any[]) => (string | number)[];
  onChange?: (value: any) => void;
  value?: any;
  filterIdBK?: string;
  filterExtendField?: (field: any) => any;
  emptyTip?: string;
}

export default function (props: Props) {
  const {
    children,
    loading_data = false,
    loading,
    filterKey = 'dimensionInfo',
    handleInject,
    componentProps,
    fieldProps,
    handleFieldLabel,
    filterIdBK,
    filterExtendField = () => ({}),
    emptyTip = '请选择字段',
  } = props;

  const { __id, componentId, dataSetConfig, uuid } = componentProps;
  const defaultValue = componentProps.defaultValue || props.defaultValue;
  const filterId = __id || componentId || filterIdBK || '';
  const linkageCenter = linkageCenterExp(uuid);
  const queryCenter = queryCenterExp(uuid);
  const relationCenter = relationCenterExp(uuid);

  const [value, _setValue] = useState<any>();

  const fieldItem: DatasetItem = useMemo(() => {
    if (fieldProps?.key) {
      return fieldProps;
    }
    return dataSetConfig?.[filterKey]?.[0] || {};
  }, [fieldProps, dataSetConfig, filterKey]);

  const dispatch = (val: any, dis = true, Release = false) => {
    if (fieldItem.columnId === undefined) return;
    if (Release || val !== value) {
      // 当前state 更新
      _setValue(val);
      // 乐高quēry 更新，数组形式
      const handleValue = Array.isArray(val) ? val : (val && [val]) || [];
      const Field = {
        columnId: fieldItem.columnId,
        key: fieldItem.key,
        dataType: fieldItem.dataType,
        fieldValue: handleValue,
        fieldLabel: handleFieldLabel?.(handleValue) || handleValue, // 目前 fieldValue\fieldLabel 值都一致
      };

      queryCenter.setQuery(filterId, {
        ...Field,
        ...filterExtendField(Field),
      });

      // 是否需要触发 onChange
      if (dis) {
        props.onChange?.(val);
      }
    }
  };

  // 初始化状态录入
  useLayoutEffect(() => {
    dispatch(
      props.value !== undefined ? props.value : defaultValue,
      true,
      true,
    );
    return () => {
      queryCenter.deleteQuery(filterId);
    };
  }, []);

  // 监听外边 value 变化时触发 setValue 事件
  useLayoutUpdateEffect(() => {
    // 不触发 外部 onchange， 避免无限递归
    dispatch(props.value, false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.value]);

  // 当字段信息\defaultValue 变化时, 重新设置 value
  useEffect(() => {
    // 这个只是默认值，要判断不为空时候才执行
    // if (defaultValue !== undefined) {
    //   dispatch(defaultValue);
    // }
    dispatch(defaultValue || []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fieldItem, defaultValue]);

  // 重置注册
  useEffect(() => {
    const resetFilter = () => {
      // 不进行diff ，直接进行修改
      dispatch(defaultValue, true, true);
    };

    linkageCenter.subscribe('reset', resetFilter);

    return () => {
      linkageCenter.unsubscribe('reset', resetFilter);
    };
  }, [fieldItem]);

  // 检测外部 loading_data 状态 来判断当前筛选器是否可用
  useLayoutEffect(() => {
    if (fieldItem && loading_data) {
      if (loading) {
        relationCenter.registerFilter(`list_filter_${filterId}`);
      } else {
        relationCenter.readyFilter(`list_filter_${filterId}`);
      }
    } else {
      relationCenter.registerFilter(`list_filter_${filterId}`);
      setTimeout(() => {
        relationCenter.readyFilter(`list_filter_${filterId}`);
      }, 10);
    }
  }, [filterId, loading_data, loading]);

  // 处理外部传入的 props 注入
  const otherProps = useMemo(() => {
    if (handleInject) {
      return handleInject({ fieldItem, value });
    } else {
      return {};
    }
  }, [fieldItem, handleInject, value]);

  const childProps = {
    value,
    fieldItem,
    onChange: function (value: any) {
      dispatch(value);
    },
    ...otherProps,
  };

  if (fieldItem.columnId === undefined) {
    // @ts-expect-error
    return <div className="lego-filter-wrap">{emptyTip}</div>;
  }

  if (typeof children === 'function') {
    return children(childProps);
  }

  return React.Children.map(children, (child) => {
    if (!React.isValidElement(child)) {
      return child;
    }

    return React.cloneElement(child, childProps);
  });
}
