import numeral from 'numeral';
import { ColumnItem, ResData } from '../../components/types';
import { formatValue, getText } from '../module/utils';
import { Aggregate } from '../setter/AnalysisConfig/constant';

/**
 *  饼图数据的处理
 * @param data 返回的数据
 * @returns
 */
/**
 *
 * _M{数字} 指标 有这个截取之前的中文 没有正常匹配title
 * _D{数字} 维度 有这个截取之前的中文 没有正常匹配title
 * _C{数字} 对比列 有这个截取之前的中文 没有正常匹配title
 */
const getSuffix = (
  title: string,
  key: string,
  index: number,
  firstData: any,
  type: string,
  suffix = '',
): string => {
  const titleField = `${title}_${type}${index}${suffix}`;
  const keyField = `${key}_${type}${index}${suffix}`;
  if (firstData[titleField] !== undefined) return titleField;
  if (firstData[keyField] !== undefined) return keyField;
  return '';
};

const getNameFTP = (measure: ColumnItem) => {
  //funnelFTPLabel
  let label = '转化率';
  try {
    const feConfig = JSON.parse(measure?.feConfig || '{}');
    label = feConfig?.funnelFTPLabel || label;
  } catch (error) {}
  return label;
};
export const transformData = (
  data: ResData,
  {
    dataSetConfig: { measureInfo },
  }: { dataSetConfig: { measureInfo: ColumnItem[] } },
) => {
  // 如果有维度，则是一个维度，一个指标
  if (data?.dimensionInfo?.[0]) {
    const { key, title } = data?.dimensionInfo?.[0] ?? {};

    const measureInfo = data?.measureInfo?.[0] ?? {};
    const { title: IndexText, key: indexK, numberFormat } = measureInfo;
    const firstData = data?.values?.[0] ?? {};

    const dimKey = getSuffix(title, key, 0, firstData, 'D', '_MN');
    const indexKey = getSuffix(IndexText, indexK, 0, firstData, 'M', '_MV');
    const indexFTPKey = getSuffix(IndexText, indexK, 0, firstData, 'M', '_FTP');
    // 判断key 是否存在
    // const accumulate = 20;
    const result = data?.values?.map((item, index) => ({
      name: item[dimKey] as string,
      // value: accumulate * data?.values.length - index * accumulate,
      value: item[indexKey],
      valueText: formatValue(
        item[indexKey] as number,
        data?.measureInfo?.[0] ?? {},
      ),
      valueFTP: numeral(item[indexFTPKey]).format('0.00%'),
      nameFTP: '转化率',
    }));
    return {
      text: getText(IndexText, measureInfo),
      data: result,
      showFTP: String(numberFormat) !== '2',
      disabledEdit: true,
      measureInfo: measureInfo ?? [],
    };
  } else {
    // 没有维度，直接都是指标
    // const accumulate = 20;
    const item = data?.values?.[0] ?? {};
    const result = data?.measureInfo?.map((measure, index) => {
      const indexKey = getSuffix(measure.title, measure.key, index, item, 'M');
      const indexFTPKey = getSuffix(
        measure.title,
        measure.key,
        index,
        item,
        'M',
        '_FTP',
      );
      const AggregateText =
        measure?.computeModeId && !measure?.isAggr
          ? `(${Aggregate[measure?.computeModeId]})`
          : '';
      return {
        name: (AggregateText + measure.title) as string,
        // value: accumulate * data.measureInfo.length - index * accumulate,
        value: item[indexKey],
        valueText: formatValue(item[indexKey] as number, measure),
        valueFTP: numeral(item[indexFTPKey]).format('0.00%'),
        nameFTP: getNameFTP(measureInfo[index]),
      };
    });
    return {
      text: '数值',
      data: result,
      showFTP: true,
      measureInfo: measureInfo ?? [],
    };
  }
};
