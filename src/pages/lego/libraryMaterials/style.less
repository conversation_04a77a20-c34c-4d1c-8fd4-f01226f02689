@media only screen and (max-width: 1024px) {
  .legoFilterDefaultRow {
    padding: 10px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
  }
}

@media only screen and (max-width: 760px) {
  .legoFilterDefaultRow {
    padding: 10px;
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
}

*, :after, :before {
  box-sizing: border-box;
}


.lce-page .legoFilterDefaultRow{
  position: sticky;
  top: 10px;
  z-index: 10;
  margin-bottom: 0 !important;
  &::before{
    content: '';
    width: 100%;
    height: calc(100% + 20px);
    background: #edeff3;
    position: absolute;
    top: -10px;
    z-index: -2;
  }
  &::after{
    content: '';
    width: 100%;
    height: 100%;
    background: #fff;
    position: absolute;
    top: 0px;
    border-radius: 8px;
    z-index: -1;
  }

  /* 城运车、下拉组件 不换行 */

  .ant-select-multiple .ant-select-selection-overflow{
    flex-wrap: nowrap !important;
  }
  .ant-select-multiple .ant-select-selection-item{
    max-width: 120px;
  }
  .ant-select-selection-search{
    max-width: 200px; // 解决搜索项过多引起整体宽度变大影响其他cell
  }
  .fd-layout-cell>.ant-select{
    width: 100%;
    max-width: 100%;
  }
  .ant-space-compact{
    width: 100%;
    display: flex;
    >.ant-select{
      width: auto !important;
      flex: 1;
      .ant-select-selection-overflow{
        flex-wrap: nowrap !important;
      }
    }
  }

}
.lce-page .fd-layout-row-flex:nth-of-type(2){
  margin-top: 10px;
}
// tips 可换行
.ant-tooltip-inner{
  word-break: break-all;
}

.blm-cascader-city .blm-eliipsis-text>span{
  display: inline;
}