import { setVariable } from '@/utils/common';

const expCenter = setVariable('__lego__bi_RelationCenter', {});

interface QueryFun {
  (): void;
}

interface RelationCenter {
  relationPool: Record<string, { id: string; query: QueryFun }[]>;
  filters: Record<string, boolean>;
  notify: (type: string, id?: string) => void; // 类型查询， all: 所有的都去查询，init: 页面初始化的查询，idString,// 通过具体的筛选器去查询
  subscribe: (type: string, id: string, query: QueryFun) => void;
  unsubscribe: (type: string, id: string) => void;
  // 初始化注册筛选器
  registerFilter: (filterId: string) => void;
  // 筛选器Ready
  readyFilter: (filterId: string) => void;
  clear: () => void;
  isCanBeQueried: () => boolean;
}
function getRelationCenter() {
  const relationCenter: RelationCenter = {
    /**
     * filterId :11
     */
    filters: {},
    relationPool: {},
    notify(type, id = '') {
      const pool = this.relationPool[type] ?? [];
      //单个查询
      if (id) {
        const item = pool.find((queryItem) => {
          return queryItem?.id === id;
        });
        if (item) {
          item.query();
        }
      } else {
        // 依次查询
        pool.forEach((queryItem) => {
          queryItem?.query?.();
        });
      }
    },
    // 订阅 global: 订阅全局查询按钮
    subscribe(type, id: string, query) {
      const pool = this.relationPool[type];
      if (pool) {
        pool.push({ id, query });
      } else {
        this.relationPool[type] = [{ id, query }];
      }
    },
    unsubscribe(type, id) {
      const pool = this.relationPool[type];
      if (pool) {
        const index = pool.findIndex((item) => item.id === id);

        if (index !== -1) {
          this.relationPool[type].splice(index, 1);
        }
      }
    },
    // 注册筛选器组件，因为，图表需要等待筛选器Ready 后才能进行查询，所以，需要先注册，然后改变值，然后ready,
    registerFilter(filterId) {
      this.filters[filterId] = false;
      // console.log('registerFilter', this.filters);
    },
    // 筛选器可以达到查询状态
    readyFilter(filterId) {
      this.filters[filterId] = true;
      const isAllReady = Object.values(this.filters).every(Boolean);
      // 筛选器加载完全
      if (isAllReady) {
        // 都去请求
        this.notify('all');
      }
    },
    clear() {
      this.filters = {};
      this.relationPool = {};
    },
    isCanBeQueried() {
      return Object.values(this.filters).every(Boolean);
    },
  };
  return relationCenter;
}

function exportFunction(reportId: string) {
  if (!reportId) {
    console.error('reportId is required');
  }
  return expCenter[reportId] || (expCenter[reportId] = getRelationCenter());
}

const funExp = getRelationCenter();
// eslint-disable-next-line guard-for-in
for (let k in funExp) {
  (expCenter as any)[k] = (funExp as any)[k];
  (exportFunction as any)[k] = (funExp as any)[k];
}

export default exportFunction;
