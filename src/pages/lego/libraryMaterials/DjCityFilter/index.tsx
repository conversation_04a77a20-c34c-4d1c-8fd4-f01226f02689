import { BLMAuthCitySelect } from '@dj-blm/lego-components';
import { useRef, useEffect, useMemo, useState } from 'react';
import { useDispatch } from '@/utils/store';
import FilterWrapper from '../wrapper/FilterWrapper';
import DispatchWrapper from '../wrapper/DDispatchWrapper';
import stateContext from '../../context/filterContext';
import { config } from '@blm/bi-lego-sdk/dist/es/utils';

interface CityFilterProps {
  __id?: string; // 预览模式
  componentId?: string; // 编辑模式
  // 数据源
  kind: 'link' | 'authOpen';
  uuid: string;
}

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  // 不参与获取数据集相关事情
  notQueryData: true,
};

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {},
  },
};

export const DjCityFilter = function (props: CityFilterProps) {
  const ref = useRef(null);
  const context = stateContext(props.uuid);
  const [state] = useDispatch(context);

  return (
    <DispatchWrapper context={context} store={'city'}>
      <FilterWrapper
        componentProps={props}
        fieldProps={useMemo(() => {
          return {
            key: 'adcode',
            columnId: 100000011,
            dataType: 0,
          };
        }, [])}
        handleFieldLabel={(val: []) => {
          try {
            return val.length ? ref.current?.getSelectedLabelName?.() : [];
          } catch (error) {
            return [];
          }
        }}
      >
        <BLMAuthCitySelect
          ref={ref}
          tenantId={state.tenant}
          request={config.get('setting.request')}
        />
      </FilterWrapper>
    </DispatchWrapper>
  );
};
