import { BLMOrgBtCarTeam } from '@blmcp/peento-businessComponents';
import { useMemo, useRef, useState, useEffect } from 'react';
import { useDispatch } from '@/utils/store';
import FilterWrapper from '../wrapper/FilterWrapper';
import DispatchWrapper from '../wrapper/DDispatchWrapper';
import stateContext from '../../context/filterContext';
import linkageCenterExp from '../module/Linkage';

interface CityFilterProps {
  __id?: string; // 预览模式
  componentId?: string; // 编辑模式
  // 数据源
  kind: 'link' | 'authOpen';
  uuid: string;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  // 不参与获取数据集相关事情
  notQueryData: true,
};

export const NewCarTeamFilter = function (props: CityFilterProps) {
  const context = stateContext(props.uuid);
  const [state, dispatch] = useDispatch(context);
  const ref = useRef(null);
  const minAuthLevel =
    window.legoMinAuthLevel || window.parent.legoMinAuthLevel || 4;
  const [kind, setKind] = useState(minAuthLevel > 2 ? 1 : 2);
  const linkageCenter = linkageCenterExp(props.uuid);

  useEffect(() => {
    return linkageCenter.subscribe('setCarTeamFilterKind', (val: string) => {
      const handle = val === 'link' ? 1 : 2;
      // 每次更新清空选择的
      if (handle !== kind) {
        console.log('setCarTeamFilterKind', val);
        setKind(handle);
        dispatch({
          type: 'clear',
        });
      }
    });
  }, [kind]);

  return (
    <DispatchWrapper context={context} store="carTeam">
      <FilterWrapper
        componentProps={props}
        fieldProps={useMemo(() => {
          return {
            key: 'car_team_id',
            columnId: *********,
            dataType: 0,
          };
        }, [])}
        handleFieldLabel={(val) => {
          return val.length ? ref.current?.getSelectedLabelName?.() : [];
          // return (val.length ? ref.current?.getSelectedOptions?.() : [])
          //   .filter((f) => !f.cityHalfAuthFlag)
          //   .map((m) => m.transportCompanyName);
        }}
        transportCompanyName
      >
        <BLMOrgBtCarTeam
          ref={ref}
          multiple
          tenantId={state.tenant}
          authType={kind} // 有关联
          deleteFlag={2} // 正常+删除
          adcodeList={state.city}
          openUaFlag
          maxLength={400}
          // showAll={true}
        />
      </FilterWrapper>
    </DispatchWrapper>
  );
};
